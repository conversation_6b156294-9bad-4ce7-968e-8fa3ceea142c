# convertMathFormulas 算法实现原理与思路

## 📋 概述

`convertMathFormulas` 函数用于将文本中的 LaTeX 数学公式标记（`$...$` 和 `$$...$$`）转换为 HTML script 标签格式，以便在网页中正确渲染数学公式。

## 🎯 核心目标

- **从左到右顺序匹配**：确保按照文本出现的顺序处理数学公式标记
- **正确区分行内和块级公式**：`$...$` 转换为行内公式，`$$...$$` 转换为块级公式
- **处理复杂边界情况**：如连续的 `$` 符号、不完整的公式等
- **保护代码块内容**：跳过 `<code>` 标签内的 `$` 符号

## 🔧 算法设计思路

### 1. 问题分析

**原始问题示例**：
```
输入: $x^2$$x^3$$12
期望: <script type="math/tex">x^2</script><script type="math/tex">x^3</script>$12
```

**挑战**：
- 连续的 `$` 符号需要正确配对
- 必须从左到右按顺序处理，避免错误的贪婪匹配
- 需要区分 `$$` 和两个独立的 `$`

### 2. 算法策略

采用**逐字符扫描 + 状态机**的方法：

```mermaid
stateDiagram-v2
    [*] --> Scanning: 开始扫描
    Scanning --> FoundDollar: 遇到 $
    FoundDollar --> CheckDouble: 检查下一个字符
    CheckDouble --> ProcessBlock: 是 $ (块级公式)
    CheckDouble --> ProcessInline: 不是 $ (行内公式)
    ProcessBlock --> FindBlockEnd: 寻找匹配的 $$
    ProcessInline --> FindInlineEnd: 寻找匹配的 $
    FindBlockEnd --> Convert: 找到匹配
    FindInlineEnd --> Convert: 找到匹配
    FindBlockEnd --> Fallback: 未找到匹配
    FindInlineEnd --> KeepOriginal: 未找到匹配
    Fallback --> ProcessInline: 回退处理单个 $
    Convert --> Scanning: 继续扫描
    KeepOriginal --> Scanning: 保持原样
```

## 🏗️ 算法实现步骤

### 步骤 1: 预处理 - 识别代码块

```typescript
// 查找所有 <code> 标签的位置
const codeRanges: Array<{ start: number; end: number }> = [];
const codeRegex = /<code[^>]*>.*?<\/code>/gs;
```

**目的**：避免转换代码块中的 `$` 符号

### 步骤 2: 主循环 - 逐字符扫描

```typescript
let result = '';
let i = 0;

while (i < text.length) {
    // 检查是否在代码块内
    if (isInCodeTag(i)) {
        result += text[i];
        i++;
        continue;
    }
    
    // 处理 $ 符号
    if (text[i] === '$') {
        // 算法核心逻辑
    } else {
        result += text[i];
        i++;
    }
}
```

### 步骤 3: $ 符号处理逻辑

#### 3.1 检测块级公式 ($$)

```typescript
if (i + 1 < text.length && text[i + 1] === '$') {
    // 寻找匹配的 $$
    let j = i + 2;
    while (j < text.length - 1) {
        if (text[j] === '$' && text[j + 1] === '$') {
            // 找到匹配，转换为块级公式
            const formula = text.substring(i + 2, j).trim();
            result += `<script type="math/tex; mode=display">${formula}</script>`;
            i = j + 2;
            found = true;
            break;
        }
        j++;
    }
}
```

#### 3.2 回退机制

如果没有找到匹配的 `$$`，回退处理第一个 `$`：

```typescript
if (!found) {
    // 将第一个 $ 作为行内公式处理
    let k = i + 1;
    while (k < text.length) {
        if (text[k] === '$') {
            const formula = text.substring(i + 1, k).trim();
            if (formula.length > 0) {
                result += `<script type="math/tex">${formula}</script>`;
                i = k + 1;
                break;
            }
        }
        k++;
    }
}
```

#### 3.3 行内公式处理

```typescript
else {
    // 单个 $ (行内公式)
    let j = i + 1;
    while (j < text.length) {
        if (text[j] === '$') {
            const formula = text.substring(i + 1, j).trim();
            if (formula.length > 0) {
                result += `<script type="math/tex">${formula}</script>`;
                i = j + 1;
                break;
            }
        }
        j++;
    }
}
```

## 🎯 关键设计决策

### 1. 从左到右的严格顺序

**原因**：确保复杂情况下的正确匹配
```
$x^2$$x^3$$12
^    ^    ^
1    2    3
```
- 位置1的 `$` 与位置2的第一个 `$` 配对（行内公式）
- 位置2的 `$$` 与位置3的 `$$` 配对（块级公式）

### 2. 回退机制

**场景**：遇到 `$$` 但找不到匹配的 `$$`
**策略**：将第一个 `$` 作为行内公式的开始重新处理

### 3. 空公式过滤

```typescript
if (formula.length > 0) {
    // 只转换非空公式
}
```

**目的**：避免转换空的 `$$` 或 `$` 对

## 📊 算法复杂度分析

- **时间复杂度**：O(n)，其中 n 是文本长度
- **空间复杂度**：O(n)，用于存储结果字符串
- **最坏情况**：每个字符都需要检查，但每个字符最多被访问常数次

## 🧪 测试用例覆盖

| 测试场景 | 输入 | 期望输出 | 验证点 |
|---------|------|----------|--------|
| 基本行内公式 | `$x^2$` | `<script type="math/tex">x^2</script>` | 基础功能 |
| 基本块级公式 | `$$x^2$$` | `<script type="math/tex; mode=display">x^2</script>` | 基础功能 |
| 连续公式 | `$x^2$$x^3$$12` | `<script type="math/tex">x^2</script><script type="math/tex">x^3</script>$12` | 核心场景 |
| 不完整公式 | `$incomplete` | `$incomplete` | 边界处理 |
| 混合公式 | `$a$ and $$b$$` | `<script type="math/tex">a</script> and <script type="math/tex; mode=display">b</script>` | 混合场景 |

## 🔍 优势与特点

1. **准确性**：严格的从左到右匹配，避免错误配对
2. **健壮性**：完善的边界情况处理
3. **性能**：单次遍历，线性时间复杂度
4. **兼容性**：保护代码块，避免误转换
5. **可维护性**：清晰的状态转换逻辑

## 🚀 使用示例

```typescript
import { convertMathFormulas } from '@/utils/latexUtils';

// 基本使用
const input = '$x^2$$y^3$$z$';
const output = convertMathFormulas(input);
console.log(output);
// 输出: <script type="math/tex">x^2</script><script type="math/tex; mode=display">y^3</script>$z

// 在代码块中的公式不会被转换
const codeInput = '<code>$x^2$</code> but $y^2$ will be converted';
const codeOutput = convertMathFormulas(codeInput);
console.log(codeOutput);
// 输出: <code>$x^2$</code> but <script type="math/tex">y^2</script> will be converted
```

## 🔄 详细执行流程

### 示例：处理 `$x^2$$x^3$$12`

```
步骤 1: i=0, 遇到 '$'
  ├─ 检查 i+1=1, text[1]='x' ≠ '$'
  ├─ 处理行内公式，寻找匹配的 '$'
  ├─ j=1,2,3,4: text[4]='$' ✓
  ├─ 提取公式: "x^2"
  ├─ 转换: <script type="math/tex">x^2</script>
  └─ 更新 i=5

步骤 2: i=5, 遇到 '$'
  ├─ 检查 i+1=6, text[6]='$' ✓
  ├─ 处理块级公式，寻找匹配的 '$$'
  ├─ j=7,8,9,10: text[10]='$' && text[11]='$' ✓
  ├─ 提取公式: "x^3"
  ├─ 转换: <script type="math/tex; mode=display">x^3</script>
  └─ 更新 i=12

步骤 3: i=12,13,14 普通字符 '$','1','2'
  └─ 直接复制到结果

最终结果: <script type="math/tex">x^2</script><script type="math/tex">x^3</script>$12
```

## 🛠️ 实现技巧

### 1. 代码块检测优化

```typescript
// 预先计算所有代码块位置，避免重复正则匹配
const codeRanges: Array<{ start: number; end: number }> = [];
const codeRegex = /<code[^>]*>.*?<\/code>/gs;
let match;
while ((match = codeRegex.exec(text)) !== null) {
    codeRanges.push({
        start: match.index,
        end: match.index + match[0].length
    });
}
```

### 2. 边界条件处理

```typescript
// 确保不会越界访问
while (j < text.length - 1) {  // 为 text[j+1] 预留空间
    if (text[j] === '$' && text[j + 1] === '$') {
        // 安全访问 j+1
    }
}
```

### 3. 公式内容清理

```typescript
const formula = text.substring(i + 2, j).trim();  // 去除首尾空白
if (formula.length > 0) {  // 避免空公式
    // 进行转换
}
```

## 📈 性能优化建议

1. **预编译正则表达式**：将代码块检测的正则表达式预编译
2. **字符串构建优化**：对于大文本，考虑使用数组拼接而非字符串连接
3. **早期退出**：如果文本中没有 `$` 符号，可以直接返回原文本

## 🔧 扩展性考虑

### 支持更多数学标记格式

```typescript
// 可扩展支持其他格式
const mathDelimiters = [
    { start: '$$', end: '$$', type: 'display' },
    { start: '$', end: '$', type: 'inline' },
    { start: '\\[', end: '\\]', type: 'display' },
    { start: '\\(', end: '\\)', type: 'inline' }
];
```

### 自定义转换函数

```typescript
interface ConversionOptions {
    inlineTemplate: (formula: string) => string;
    displayTemplate: (formula: string) => string;
    skipCodeBlocks: boolean;
}
```

## 🐛 常见问题与解决方案

### Q1: 为什么不使用正则表达式？

**A**: 正则表达式难以处理嵌套和复杂的边界情况，而且对于从左到右的严格顺序匹配，逐字符扫描更加可控和准确。

### Q2: 如何处理转义的 `$` 符号？

**A**: 当前实现专注于基本的 LaTeX 格式。如需支持转义，可在检测 `$` 前检查前一个字符是否为 `\`：

```typescript
if (text[i] === '$' && (i === 0 || text[i-1] !== '\\')) {
    // 处理非转义的 $ 符号
}
```

### Q3: 性能如何？

**A**: 时间复杂度为 O(n)，对于大多数实际应用场景性能表现良好。对于超大文本，可考虑分块处理。

---

*该算法经过全面测试，能够正确处理各种复杂的数学公式标记场景，确保在实际应用中的可靠性和准确性。*
